require('dotenv').config();
const express = require('express');
const { Client, LocalAuth } = require('whatsapp-web.js');
const qrcode = require('qrcode');
const path = require('path');
const authenticate = require('./utils/auth');

const app = express();
app.use(express.json());
app.use(express.static('public'));

// Session storage
const activeSessions = new Map(); // { sessionId: { client, qrData, ready } }

// ========================
// Documentation Endpoint
// ========================
app.get('/', (req, res) => {
  const baseUrl = `${req.protocol}://${req.get('host')}`;
  
  res.json({
    api: "WhatsApp Multi-Session API",
    version: "1.0.0",
    documentation: {
      description: "API for managing multiple WhatsApp connections simultaneously",
      endpoints: [
        {
          method: "GET",
          path: "/",
          description: "API documentation"
        },
        {
          method: "POST",
          path: "/sessions",
          description: "Create new WhatsApp session",
          parameters: {
            sessionId: "(optional) Custom session identifier"
          },
          example: `${baseUrl}/sessions`
        },
        {
          method: "GET",
          path: "/sessions/:sessionId/qr",
          description: "Get QR code for session",
          example: `${baseUrl}/sessions/my-session/qr`
        },
        {
          method: "POST",
          path: "/sessions/:sessionId/send",
          description: "Send WhatsApp message",
          headers: {
            "X-API-Key": "Your API key"
          },
          parameters: {
            number: "Phone number (with country code, no +)",
            message: "Text message to send"
          },
          example: `${baseUrl}/sessions/my-session/send`
        },
        {
          method: "GET",
          path: "/sessions",
          description: "List all active sessions",
          example: `${baseUrl}/sessions`
        }
      ]
    },
    notes: [
      "1. Create session first to get QR code",
      "2. Scan QR with WhatsApp mobile app",
      "3. Use session ID to send messages"
    ]
  });
});

// ========================
// Session Management
// ========================
app.post('/sessions', (req, res) => {
  const sessionId = req.body.sessionId || `session-${Date.now()}`;
  
  if (activeSessions.has(sessionId)) {
    return res.status(400).json({ error: 'Session already exists' });
  }

  const client = new Client({
    authStrategy: new LocalAuth({ clientId: sessionId }),
    puppeteer: { 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    }
  });

  let qrData = null;
  let ready = false;

  client.on('qr', async (qr) => {
    qrData = await qrcode.toDataURL(qr);
    activeSessions.set(sessionId, { client, qrData, ready });
  });

  client.on('ready', () => {
    ready = true;
    activeSessions.set(sessionId, { client, qrData, ready });
    console.log(`Session ${sessionId} ready!`);
  });

  client.on('disconnected', () => {
    activeSessions.delete(sessionId);
    console.log(`Session ${sessionId} disconnected`);
  });

  client.initialize();
  
  res.json({ 
    success: true,
    sessionId,
    qrUrl: `${req.protocol}://${req.get('host')}/sessions/${sessionId}/qr`,
    message: 'Scan QR code to connect WhatsApp account'
  });
});

// ========================
// QR Code Endpoint
// ========================
app.get('/sessions/:sessionId/qr', (req, res) => {
  const session = activeSessions.get(req.params.sessionId);
  
  if (!session) return res.status(404).json({ error: 'Session not found' });
  if (!session.qrData) return res.status(425).json({ error: 'QR not generated yet' });
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>WhatsApp Connection - ${req.params.sessionId}</title>
      <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
        #qr-container { margin: 20px auto; max-width: 300px; }
        #status { 
          padding: 10px; 
          margin: 20px auto;
          max-width: 300px;
          border-radius: 5px;
        }
        .ready { background: #4CAF50; color: white; }
        .waiting { background: #FFC107; }
      </style>
      <script>
        async function checkStatus() {
          try {
            const response = await fetch('/sessions/${req.params.sessionId}/status');
            if (response.ok) {
              const data = await response.json();
              if (data.ready) {
                document.getElementById('status').className = 'ready';
                document.getElementById('status').innerHTML = '✅ WhatsApp Connected!';
              } else {
                setTimeout(checkStatus, 3000);
              }
            }
          } catch (error) {
            console.error('Status check failed:', error);
          }
        }
        window.onload = checkStatus;
      </script>
    </head>
    <body>
      <h1>WhatsApp Connection</h1>
      <h2>Session: ${req.params.sessionId}</h2>
      <div id="qr-container">
        <img src="${session.qrData}" width="300" height="300"/>
      </div>
      <div id="status" class="waiting">⌛ Waiting for QR scan...</div>
      <p>1. Open WhatsApp on your phone</p>
      <p>2. Tap <strong>Linked Devices</strong></p>
      <p>3. Tap <strong>Link a Device</strong></p>
      <p>4. Scan this QR code</p>
    </body>
    </html>
  `);
});

// ========================
// Status Check
// ========================
app.get('/sessions/:sessionId/status', (req, res) => {
  const session = activeSessions.get(req.params.sessionId);
  res.json({ 
    exists: !!session,
    ready: session?.ready || false
  });
});

// ========================
// Send Message (Authenticated)
// ========================
app.post('/sessions/:sessionId/send', authenticate, async (req, res) => {
  const session = activeSessions.get(req.params.sessionId);
  
  if (!session) return res.status(404).json({ error: 'Session not found' });
  if (!session.ready) return res.status(425).json({ error: 'WhatsApp not connected' });

  try {
    const { number, message } = req.body;
    const chatId = number.includes('@') ? number : `${number}@c.us`;
    const sentMsg = await session.client.sendMessage(chatId, message);
    
    res.json({ 
      success: true,
      messageId: sentMsg.id.id,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// ========================
// List Sessions
// ========================
app.get('/sessions', (req, res) => {
  res.json({
    sessions: Array.from(activeSessions.keys()).map(sessionId => ({
      sessionId,
      ready: activeSessions.get(sessionId).ready,
      qrUrl: `${req.protocol}://${req.get('host')}/sessions/${sessionId}/qr`
    }))
  });
});

// Start server
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`API Documentation: http://localhost:${PORT}`);
});


// Create a new WhatsApp session
// curl -X POST http://localhost:3000/sessions \
  // -H "Content-Type: application/json" \
  // -d '{"sessionId":"my-business-account"}'

// Scan QR code
// http://localhost:3000/sessions/my-business-account/qr


  // Send message
  // curl -X POST http://localhost:3000/sessions/my-business-account/send \
  // -H "Content-Type: application/json" \
  // -H "X-API-Key: your_secret_key_1" \
  // -d '{"number":"**********","message":"Hello from API!"}'



  // List active sessions
  // curl http://localhost:3000/sessions
