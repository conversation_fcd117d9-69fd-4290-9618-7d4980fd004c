const fs = require('fs');
const path = require('path');

const DATA_DIR = path.join(__dirname, '../data');
const MESSAGES_FILE = path.join(DATA_DIR, 'messages.json');

// Ensure data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Initialize messages file
if (!fs.existsSync(MESSAGES_FILE)) {
  fs.writeFileSync(MESSAGES_FILE, '[]');
}

module.exports = {
  saveMessage: async (message) => {
    const messages = JSON.parse(fs.readFileSync(MESSAGES_FILE));
    messages.push(message);
    fs.writeFileSync(MESSAGES_FILE, JSON.stringify(messages, null, 2));
  },
  getMessages: async () => {
    return JSON.parse(fs.readFileSync(MESSAGES_FILE));
  }
};